const { program} = require("commander")
const { execSync } = require("node:child_process");
const {} = require("typeorm")

program
  .command("migration:create")
  .description("Create a new migration file")
  .argument("<name>", "The name of the migration")
  .action(async (name) => {
    try {
      console.log("Generate a new migration file", name);
      console.log(execSync(`typeorm migration:create src/migrations/${name}`).toString())
    } catch (error) {
      console.error("Error generating migration:", error);
      process.exit(1);
    }
  });

program.parse()