import { INestApplication, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { ConfigCode } from "../constants";

const logger = new Logger("Bootstrap");

export function docs(application: INestApplication): INestApplication {
  const config = new DocumentBuilder()
    .addBearerAuth()
    .setTitle("API Documentation")
    .build();
  const document = SwaggerModule.createDocument(application, config);
  SwaggerModule.setup("docs", application, document, {
    url: "docs",
    jsonDocumentUrl: "docs.json",
    yamlDocumentUrl: "docs.yaml",
  });

  return application;
}

export function cors(application: INestApplication): INestApplication {
  const config = application.get(ConfigService);
  const allowedOrigins = config
    .get<string>(ConfigCode.ALLOWED_ORIGINS, "")
    .split(",")
    .filter(Boolean);

  application.enableCors({
    origin: allowedOrigins,
    credentials: true,
    maxAge: 3600,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  });

  return application;
}

export async function listen(
  application: INestApplication,
): Promise<INestApplication> {
  const config = application.get(ConfigService);
  const port = config.get<number>(ConfigCode.PORT, 8080);

  await application.listen(port);
  logger.log(`Application is running on: ${await application.getUrl()}`);
  return application;
}
