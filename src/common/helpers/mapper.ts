import { Pagination } from "nestjs-typeorm-paginate";
import { ClassConstructor, plainToInstance } from "class-transformer";

export function paginationMapTo<D>(destinationType: ClassConstructor<D>) {
  return <S>(pagination: Pagination<S>): Pagination<D> =>
    paginationMap(pagination, destinationType);
}

export function paginationMap<S, D>(
  pagination: Pagination<S>,
  destinationType: ClassConstructor<D>,
): Pagination<D> {
  return {
    ...pagination,
    items: mapArray(pagination.items, destinationType),
  };
}

export function mapTo<D>(destinationType: ClassConstructor<D>) {
  return <S>(source: S): D => map(source, destinationType);
}

export function map<S, D>(source: S, destinationType: ClassConstructor<D>): D {
  return plainToInstance(destinationType, source);
}

export function mapArrayTo<D>(destinationType: ClassConstructor<D>) {
  return <S>(source: S[]): D[] => mapArray(source, destinationType);
}

export function mapArray<S, D>(
  source: S[],
  destinationType: ClassConstructor<D>,
): D[] {
  return plainToInstance(destinationType, source);
}
