import { ClsService, ClsServiceManager } from "nestjs-cls";
import { System } from "../constants";
import { randomUUID } from "crypto";
import { Request, Response } from "express";

export const RequestContext = ClsServiceManager.getClsService();

export const setupRequestContext = (
  cls: ClsService,
  request: Request,
  response: Response,
) => {
  const traceId = request.headers[System.traceId] || randomUUID();
  request.headers[System.traceId] = traceId;
  response.setHeader(System.traceId, traceId);
  cls.set(System.traceId, traceId);
  cls.set(System.method, request.method);
  cls.set(System.path, request.originalUrl);
};
