import { WinstonModule } from "nest-winston";
import { format, transports } from "winston";
import { System } from "../constants";
import { RequestContext } from "./request-context";

export const getLogger = (logLevel = "info") =>
  WinstonModule.createLogger({
    level: logLevel,
    format: format.combine(
      format.timestamp(),
      format.ms(),
      format.errors({
        stack: true,
      }),
      format.printf((info) => {
        let message = `${info.timestamp} - ${info.level.toUpperCase()} ${process.pid} [${info.context}] ${info.message}`;
        const trace = [
          ["trace_id", RequestContext.get(System.traceId)],
          ["method", RequestContext.get(System.method)],
          ["path", RequestContext.get(System.path)],
          ["user_id", RequestContext.get(System.userId)],
          ["username", RequestContext.get(System.username)],
        ]
          .filter(([, value]) => <PERSON><PERSON>an(value))
          .map(([key, value]) => `${key}=${value}`)
          .join(";");

        if (trace) {
          message += ` [${trace}]`;
        }
        message += ` ${info.ms}`;
        if (info.stack) {
          message += `\n${info.stack}`;
        }
        return message;
      }),
    ),
    transports: [new transports.Console()],
  });
