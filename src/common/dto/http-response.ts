import { HttpException } from "@nestjs/common";

export class HttpResponse<T = unknown> {
  message: string;
  data?: T;
  errors?: unknown;
  meta?: Record<string, unknown>;

  timestamp: Date;

  public static of<T>(
    data: T,
    message = "OK",
    error?: Record<any, any>,
    metadata?: Record<string, unknown>,
  ): HttpResponse<T> {
    const response = new HttpResponse<T>();
    response.data = data;
    response.message = message;
    response.errors = error;
    response.timestamp = new Date();
    response.meta = metadata;
    return response;
  }

  public static ofHttpException(e: HttpException): HttpResponse {
    const response: string | object = e.getResponse();
    if (typeof response === "string") {
      return HttpResponse.of(null, response);
    } else if (response instanceof HttpResponse) {
      return response;
    }
    return HttpResponse.of(
      null,
      response["message"],
      response["error"],
      response["meta"],
    );
  }
}
