import { HttpException as NestHttpException, HttpStatus } from "@nestjs/common";
import { ErrorMessage } from "../constants";

export class HttpException extends NestHttpException {
  public static badRequest(
    error: string = ErrorMessage.BAD_REQUEST,
  ): HttpException {
    return new HttpException(error, HttpStatus.BAD_REQUEST);
  }

  public static unauthorized(): HttpException {
    return new HttpException(
      ErrorMessage.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED,
    );
  }

  public static forbidden(
    error: string = ErrorMessage.FORBIDDEN,
  ): HttpException {
    return new HttpException(error, HttpStatus.FORBIDDEN);
  }

  public static notFound(
    error: string = ErrorMessage.NOT_FOUND,
  ): HttpException {
    return new HttpException(error, HttpStatus.NOT_FOUND);
  }

  public static internalServerError(
    error: string = ErrorMessage.INTERNAL_SERVER_ERROR,
  ): HttpException {
    return new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
