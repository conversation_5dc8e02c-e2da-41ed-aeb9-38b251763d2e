import { AuthGuard, PassportStrategy } from "@nestjs/passport";
import { Injectable } from "@nestjs/common";
import { HttpException } from "../exceptions";
import { ExtractJwt, Strategy } from "passport-jwt";
import { ConfigService } from "@nestjs/config";
import { User } from "../interfaces";
import { passportJwtSecret } from "jwks-rsa";
import { Reflector } from "@nestjs/core";
import { System, Token } from "../constants";
import { RequestContext } from "../helpers";
import { ConfigCode } from "../constants";

const jwtStrategyName = "jwt";

@Injectable()
export class JwtGuard extends AuthGuard(jwtStrategyName) {
  constructor(private readonly reflector: Reflector) {
    super();
  }

  handleRequest(err: any, user: any, info: any, context: any) {
    const isPublic =
      this.reflector.get<boolean>(Token.Public, context.getHandler()) ??
      this.reflector.get<boolean>(Token.Public, context.getClass());

    if (!isPublic) {
      if (err || !user) {
        throw HttpException.unauthorized();
      }
    }

    RequestContext.set(System.userId, user.id);
    RequestContext.set(System.username, user.username);

    return user;
  }
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, jwtStrategyName) {
  constructor(configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      issuer: configService.get(ConfigCode.JWT_ISSUER, undefined),
      audience: configService.get(ConfigCode.JWT_AUDIENCE, undefined),
      secretOrKeyProvider: passportJwtSecret({
        jwksUri: configService.getOrThrow(ConfigCode.JWT_JWKS_URI),
        cache: true,
      }),
    });
  }

  validate(payload: Record<string, any>): User {
    return {
      id: payload["sub"],
      email: payload["email"],
      username: payload["preferred_username"],
      roles: payload["roles"] || [],
    };
  }
}
