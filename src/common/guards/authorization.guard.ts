import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Authority, Token } from "../constants";
import { Request } from "express";
import { User } from "../interfaces";

@Injectable()
export class AuthorizationGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): Promise<boolean> {
    const request: Request = context.switchToHttp().getRequest();
    const requiredAuthorities: Authority[] =
      this.reflector.get(Token.RequiredAuthorities, context.getHandler()) ??
      this.reflector.get(Token.RequiredAuthorities, context.getClass());

    if (!requiredAuthorities || requiredAuthorities.length === 0) {
      return Promise.resolve(true);
    }

    return this.validateRequest(request, requiredAuthorities);
  }

  async validateRequest(
    request: Request,
    requiredAuthorities: string[],
  ): Promise<boolean> {
    const user = request["user"] as User;
    // TODO: Implement actual permission validation logic

    return true;
  }
}
