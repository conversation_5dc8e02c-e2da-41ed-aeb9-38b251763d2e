import { BaseExceptionFilter } from "@nestjs/core";
import {
  ArgumentsHost,
  Catch,
  HttpException,
  HttpStatus,
  Logger,
} from "@nestjs/common";
import { Response } from "express";
import { HttpResponse } from "../dto";

@Catch(HttpException)
export class HttpExceptionFilter extends BaseExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  constructor() {
    super();
  }

  catch(e: HttpException, host: ArgumentsHost) {
    if (host.getType() === "http") {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      if (e.getStatus() >= HttpStatus.INTERNAL_SERVER_ERROR.valueOf()) {
        this.logger.error(`[${e.getStatus()}] ${e.message}`, e.stack);
      }

      return response
        .status(e.getStatus())
        .json(HttpResponse.ofHttpException(e));
    }
    return super.catch(e, host);
  }
}
