export enum ErrorCode {
  BadRequest = "BAD_REQUEST",
  Unauthorized = "UNAUTHORIZED",
  Forbidden = "FORBIDDEN",
  NotFound = "NOT_FOUND",
  InternalServerError = "INTERNAL_SERVER_ERROR",
}

export const ErrorMessage: Record<ErrorCode, string> = {
  [ErrorCode.BadRequest]: "Bad request. Please check your input.",
  [ErrorCode.Unauthorized]: "Unauthorized. Please log in to access this resource.",
  [ErrorCode.Forbidden]:
    "Forbidden. You do not have permission to access this resource.",
  [ErrorCode.NotFound]: "Not found. The requested resource could not be found.",
  [ErrorCode.InternalServerError]:
    "Internal server error. Please try again later.",
};
