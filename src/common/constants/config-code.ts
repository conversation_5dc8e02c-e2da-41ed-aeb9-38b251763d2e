export enum ConfigCode {
  PORT = "PORT",
  NODE_ENV = "NODE_ENV",
  ALLOWED_ORIGINS = "ALLOWED_ORIGINS",
  LOG_LEVEL = "LOG_LEVEL",

  DB_HOST = "DB_HOST",
  DB_PORT = "DB_PORT",
  DB_USER = "DB_USER",
  DB_PASSWORD = "DB_PASSWORD",
  DB_NAME = "DB_NAME",

  DB_MIGRATION_RUN = "DB_MIGRATION_RUN",
  DB_MAX_CONNECTIONS = "DB_MAX_CONNECTIONS",
  DB_MIN_CONNECTIONS = "DB_MIN_CONNECTIONS",
  DB_IDLE_TIMEOUT = "DB_IDLE_TIMEOUT",
  DB_CONNECTION_TIMEOUT = "DB_CONNECTION_TIMEOUT",

  JWT_ISSUER = "JWT_ISSUER",
  JWT_AUDIENCE = "JWT_AUDIENCE",
  JWT_JWKS_URI = "JWT_JWKS_URI",
}
