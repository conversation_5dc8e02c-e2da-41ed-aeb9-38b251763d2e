import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from "@nestjs/common";
import { map, Observable } from "rxjs";
import { Pagination } from "nestjs-typeorm-paginate";
import { HttpResponse } from "../dto";
import { Reflector } from "@nestjs/core";
import { Token } from "../constants";
import {
  ClassConstructor,
  plainToClass,
  plainToInstance,
} from "class-transformer";

const JsonContentType = "application/json";

@Injectable()
export class HttpResponseInterceptor implements NestInterceptor {
  constructor(private readonly reflector: Reflector) {}

  private handleHttpRequest(context: ExecutionContext, next: CallHandler) {
    const c = context.switchToHttp();
    if (!c) {
      return next.handle();
    }
    const response = c.getResponse();
    const contentType = response.get("Content-Type") ?? JsonContentType;

    const targetResponseModel: ClassConstructor<unknown> | undefined =
      this.reflector.get(Token.ResponseModel, context.getHandler());

    return next.handle().pipe(
      map((data) => {
        if (
          !(data instanceof HttpResponse) &&
          contentType === JsonContentType
        ) {
          if (data instanceof Pagination) {
            return HttpResponse.of(
              targetResponseModel
                ? plainToInstance(targetResponseModel, data.items)
                : data.items,
              "OK",
              undefined,
              {
                ...data.meta,
                ...data.links,
              },
            );
          }
          return HttpResponse.of(
            targetResponseModel
              ? plainToInstance(targetResponseModel, data)
              : data,
          );
        }
        return data;
      }),
    );
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    switch (context.getType()) {
      case "http":
        return this.handleHttpRequest(context, next);
      default:
        return next.handle();
    }
  }
}
