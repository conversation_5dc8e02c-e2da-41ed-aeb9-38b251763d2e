import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ClsModule } from "nestjs-cls";
import { setupRequestContext } from "../../common/helpers";
import { HealthModule } from "./health/health.module";
import { DatabaseModule } from "./database/database.module";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
        setup: setupRequestContext,
      },
    }),
    HealthModule,
    DatabaseModule,
  ],
  providers: [],
})
export class CoreModule {}
