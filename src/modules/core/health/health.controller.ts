import { Controller, Get } from "@nestjs/common";
import { HealthCheckService, TypeOrmHealthIndicator } from "@nestjs/terminus";
import { ApiTags } from "@nestjs/swagger";
import { Public } from "../../../common";

@ApiTags("Health Check")
@Public()
@Controller("health")
export class HealthController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    private typeOrmHealthIndicator: TypeOrmHealthIndicator,
  ) {}

  @Get()
  getHealth() {
    return this.healthCheckService.check([
      () => this.typeOrmHealthIndicator.pingCheck("database"),
    ]);
  }
}
