import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ConfigService } from "@nestjs/config";
import {
  addTransactionalDataSource,
  getDataSourceByName,
} from "typeorm-transactional";
import { DataSource } from "typeorm";
import { ConfigCode } from "../../../common";

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          type: "postgres",
          host: configService.get(ConfigCode.DB_HOST, "localhost"),
          port: configService.get(ConfigCode.DB_PORT, 5432),
          username: configService.get(ConfigCode.DB_USER, "postgres"),
          password: configService.getOrThrow(ConfigCode.DB_PASSWORD),
          database: configService.getOrThrow(ConfigCode.DB_NAME),
          migrations: ["dist/migrations/*.js"],
          migrationsTableName: "_migrations",
          migrationsRun:
            configService.get(ConfigCode.DB_MIGRATION_RUN, "true") === "true",
          logging: configService.get(ConfigCode.NODE_ENV) === "development",
          autoLoadEntities: true,
          synchronize: false,
          extra: {
            max: configService.get(ConfigCode.DB_MAX_CONNECTIONS, 10),
            min: configService.get(ConfigCode.DB_MIN_CONNECTIONS, 3),
            idleTimeoutMillis: configService.get(
              ConfigCode.DB_IDLE_TIMEOUT,
              3e4,
            ),
            connectionTimeoutMillis: configService.get(
              ConfigCode.DB_CONNECTION_TIMEOUT,
              1e4,
            ),
          },
        };
      },
      dataSourceFactory: async (options) => {
        const existingDataSource = getDataSourceByName("default");
        if (existingDataSource) {
          return existingDataSource;
        }
        if (!options) {
          throw new Error("Database options are not defined");
        }
        const dataSource = await new DataSource(options).initialize();
        return addTransactionalDataSource(dataSource);
      },
    }),
  ],
})
export class DatabaseModule {}
