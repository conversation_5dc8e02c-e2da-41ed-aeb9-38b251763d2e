import { UserService } from "./interfaces";
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { UserEntity } from "./entities";
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from "nestjs-typeorm-paginate";
import { User } from "./interfaces";

@Injectable()
export class UserServiceImpl extends UserService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
  ) {
    super();
  }

  getAllUsers(options: IPaginationOptions): Promise<Pagination<User>> {
    return paginate(this.userRepository, options);
  }
}
