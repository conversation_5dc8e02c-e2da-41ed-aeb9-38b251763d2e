import {
  Controller,
  DefaultValuePipe,
  Get,
  ParseIntPipe,
  Query,
} from "@nestjs/common";
import { UserService } from "./interfaces";
import { UserListDto } from "./dtos";
import { ResponseModel } from "../../common";

@Controller("users")
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ResponseModel(UserListDto)
  async getAllUsers(
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number = 10,
  ) {
    return await this.userService.getAllUsers({
      page: 0,
      limit: 10,
    });
  }
}
