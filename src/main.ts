import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { initializeTransactionalContext } from "typeorm-transactional";
import { ConfigCode } from "./common";
import { getLogger } from "./common/helpers/logger";
import { cors, docs, listen } from "./common/helpers/bootstrap";

async function bootstrap() {
  initializeTransactionalContext();
  return NestFactory.create(AppModule, {
    autoFlushLogs: true,
    logger: getLogger(process.env[ConfigCode.LOG_LEVEL]),
  })
    .then(docs)
    .then(cors)
    .then(listen);
}

bootstrap().catch(console.error);
