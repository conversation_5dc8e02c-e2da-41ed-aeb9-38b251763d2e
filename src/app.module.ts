import {
  ClassSerializerInterceptor,
  Module,
  Provider,
  ValidationPipe,
} from "@nestjs/common";
import { CoreModule } from "./modules/core/core.module";
import { HttpResponseInterceptor } from "./common/interceptors";
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from "@nestjs/core";
import {
  AuthorizationGuard,
  HttpExceptionFilter,
  JwtGuard,
  JwtStrategy,
} from "./common";
import { ACTIVE_MODULES } from "./modules";

/**
 * Global providers: interceptors, guards, pipes, filters
 */
const providerFactoryOf = (provide: string) => (cls: any) => ({
  provide,
  useClass: cls,
});
const interceptors: Provider[] = [
  HttpResponseInterceptor,
  ClassSerializerInterceptor,
].map(providerFactoryOf(APP_INTERCEPTOR));
// const guards: Provider[] = [JwtGuard, AuthorizationGuard].map(
//   providerFactoryOf(APP_GUARD),
// );
const guards: Provider[] = [];
const pipes: Provider[] = [ValidationPipe].map(providerFactoryOf(APP_PIPE));
const filters: Provider[] = [HttpExceptionFilter].map(
  providerFactoryOf(APP_FILTER),
);

@Module({
  imports: [CoreModule, ...ACTIVE_MODULES],
  providers: [...interceptors, ...guards, ...pipes, ...filters, JwtStrategy],
})
export class AppModule {}
